[05-Aug-2025 01:52:08 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:52:09 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:52:13 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:52:19 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:52:19 UTC] Notion to WordPress: 导入文章数量: 7
[05-Aug-2025 01:52:19 UTC] Notion to WordPress: 已发布文章数量: 5
[05-Aug-2025 01:52:19 UTC] Notion to WordPress: 统计信息获取成功: {"imported_count":7,"published_count":5,"last_update":"2025\u5e748\u67083\u65e5 \u4e0b\u5348 3:37","next_run":"2025\u5e748\u67083\u65e5 \u4e0a\u5348 11:32"}
[05-Aug-2025 01:52:29 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:52:39 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:52:49 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:52:49 UTC] Notion to WordPress: 导入文章数量: 7
[05-Aug-2025 01:52:49 UTC] Notion to WordPress: 已发布文章数量: 5
[05-Aug-2025 01:52:49 UTC] Notion to WordPress: 统计信息获取成功: {"imported_count":7,"published_count":5,"last_update":"2025\u5e748\u67083\u65e5 \u4e0b\u5348 3:37","next_run":"2025\u5e748\u67083\u65e5 \u4e0a\u5348 11:32"}
[05-Aug-2025 01:52:59 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:53:18 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:53:19 UTC] Notion to WordPress: 导入文章数量: 7
[05-Aug-2025 01:53:19 UTC] Notion to WordPress: 已发布文章数量: 5
[05-Aug-2025 01:53:19 UTC] Notion to WordPress: 统计信息获取成功: {"imported_count":7,"published_count":5,"last_update":"2025\u5e748\u67083\u65e5 \u4e0b\u5348 3:37","next_run":"2025\u5e748\u67083\u65e5 \u4e0a\u5348 11:32"}
[05-Aug-2025 01:53:22 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:53:29 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:53:39 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:53:46 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:53:50 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:53:50 UTC] Notion to WordPress: 导入文章数量: 7
[05-Aug-2025 01:53:50 UTC] Notion to WordPress: 已发布文章数量: 5
[05-Aug-2025 01:53:50 UTC] Notion to WordPress: 统计信息获取成功: {"imported_count":7,"published_count":5,"last_update":"2025\u5e748\u67083\u65e5 \u4e0b\u5348 3:37","next_run":"2025\u5e748\u67083\u65e5 \u4e0a\u5348 11:32"}
[05-Aug-2025 01:53:59 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:54:09 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:54:27 UTC] Notion to WordPress: 导入文章数量: 7
[05-Aug-2025 01:54:27 UTC] Notion to WordPress: 已发布文章数量: 5
[05-Aug-2025 01:54:27 UTC] Notion to WordPress: 统计信息获取成功: {"imported_count":7,"published_count":5,"last_update":"2025\u5e748\u67083\u65e5 \u4e0b\u5348 3:37","next_run":"2025\u5e748\u67083\u65e5 \u4e0a\u5348 11:32"}
[05-Aug-2025 01:54:31 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:54:35 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
[05-Aug-2025 01:54:39 UTC] PHP Fatal error:  Uncaught Error: Class "NTWP\Core\Foundation\Performance\ProgressTracker" not found in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php:248
Stack trace:
#0 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php(173): NTWP\Core\Task\ModernAsyncEngine::initializeComponents()
#1 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\admin\Controllers\AdminController.php(2032): NTWP\Core\Task\ModernAsyncEngine::getStatus()
#2 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(324): NTWP\Admin\Controllers\AdminController->handle_get_queue_status('')
#3 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#4 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#5 C:\Users\<USER>\Local Sites\frankloong\app\public\wp-admin\admin-ajax.php(192): do_action('wp_ajax_notion_...')
#6 {main}
  thrown in C:\Users\<USER>\Local Sites\frankloong\app\public\wp-content\plugins\Notion-to-WordPress\includes\core\Task\ModernAsyncEngine.php on line 248
