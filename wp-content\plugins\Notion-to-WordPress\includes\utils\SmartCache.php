<?php
declare(strict_types=1);

namespace NTWP\Utils;

use NTWP\Infrastructure\Cache\CacheManager;

/**
 * Notion 智能缓存管理器类 - CacheManager适配器
 *
 * 为了向后兼容，此类现在作为CacheManager的适配器
 * 所有缓存操作都委托给统一的CacheManager处理
 *
 * @deprecated 2.0.0-beta.1 请使用 NTWP\Infrastructure\Cache\CacheManager
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class SmartCache {

    /**
     * 缓存类型配置 - 委托给CacheManager
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::CACHE_TYPES
     */
    const CACHE_TYPES = [
        'user_info' => [
            'ttl' => 3600,      // 1小时
            'description' => '用户信息缓存'
        ],
        'database_structure' => [
            'ttl' => 1800,      // 30分钟
            'description' => '数据库结构缓存'
        ],
        'page_content' => [
            'ttl' => 300,       // 5分钟
            'description' => '页面内容缓存'
        ],
        'api_response' => [
            'ttl' => 60,        // 1分钟
            'description' => 'API响应缓存'
        ]
    ];

    /**
     * 缓存性能常量 - 保持向后兼容
     * @deprecated 2.0.0-beta.1 这些常量现在由CacheManager管理
     */
    const L1_CACHE_TTL = 60;                  // L1缓存生存时间（1分钟）
    const L1_CACHE_SIZE_LIMIT = 100;          // L1缓存大小限制
    const L1_CACHE_ITEM_SIZE_THRESHOLD = 10240; // L1缓存项目大小阈值（10KB）
    const DEFAULT_CACHE_TTL = 300;            // 默认缓存时间（5分钟）
    
    /**
     * 生成缓存键 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @param string $type 缓存类型
     * @param string $identifier 标识符
     * @param array $params 额外参数
     * @return string 缓存键
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::generate_cache_key_smart()
     */
    public static function generate_cache_key(string $type, string $identifier, array $params = []): string {
        // 委托给CacheManager的兼容方法
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::generate_cache_key_smart($type, $identifier, $params);
        }

        // 后备实现（如果CacheManager不可用）
        $key_parts = [
            'notion_cache',
            $type,
            $identifier
        ];

        if (!empty($params)) {
            $key_parts[] = md5(serialize($params));
        }

        return implode('_', $key_parts);
    }
    
    /**
     * 设置缓存 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @param string $type 缓存类型
     * @param string $identifier 标识符
     * @param mixed $data 缓存数据
     * @param array $params 额外参数
     * @param int $custom_ttl 自定义TTL
     * @return bool 设置是否成功
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::set_tiered()
     */
    public static function set(string $type, string $identifier, $data, array $params = [], int $custom_ttl = null): bool {
        if (!self::is_cache_enabled()) {
            return false;
        }

        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::set_tiered($type, $identifier, $data, $params, $custom_ttl);
        }

        // 后备实现（如果CacheManager不可用）
        $cache_key = self::generate_cache_key($type, $identifier, $params);
        $ttl = $custom_ttl ?? self::CACHE_TYPES[$type]['ttl'] ?? self::DEFAULT_CACHE_TTL;

        $cache_data = [
            'data' => $data,
            'timestamp' => time(),
            'ttl' => $ttl,
            'type' => $type,
            'identifier' => $identifier
        ];

        return set_transient($cache_key, $cache_data, $ttl);
    }
    
    /**
     * 获取缓存 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @param string $type 缓存类型
     * @param string $identifier 标识符
     * @param array $params 额外参数
     * @return mixed 缓存数据或false
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::get_tiered()
     */
    public static function get(string $type, string $identifier, array $params = []) {
        if (!self::is_cache_enabled()) {
            return false;
        }

        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::get_tiered($type, $identifier, $params);
        }

        // 后备实现（如果CacheManager不可用）
        $cache_key = self::generate_cache_key($type, $identifier, $params);
        $cache_data = get_transient($cache_key);

        if ($cache_data === false) {
            return false;
        }

        // 检查缓存是否过期
        if (isset($cache_data['timestamp'], $cache_data['ttl'])) {
            $age = time() - $cache_data['timestamp'];
            if ($age > $cache_data['ttl']) {
                delete_transient($cache_key);
                return false;
            }
        }

        return $cache_data['data'] ?? $cache_data;
    }
    
    /**
     * 删除缓存
     *
     * @since 2.0.0-beta.1
     * @param string $type 缓存类型
     * @param string $identifier 标识符
     * @param array $params 额外参数
     * @return bool 删除是否成功
     */
    public static function delete(string $type, string $identifier, array $params = []): bool {
        $cache_key = self::generate_cache_key($type, $identifier, $params);
        $result = delete_transient($cache_key);
        
        if ($result) {
            self::$cache_stats['deletes']++;
            self::update_cache_size();
        }
        
        return $result;
    }
    
    /**
     * 清理指定类型的所有缓存
     *
     * @since 2.0.0-beta.1
     * @param string $type 缓存类型
     * @return int 清理的缓存数量
     */
    public static function clear_type(string $type): int {
        global $wpdb;
        
        $pattern = 'notion_cache_' . $type . '_%';
        $deleted = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} 
                WHERE option_name LIKE %s",
                '_transient_' . $pattern
            )
        );
        
        // 同时删除超时记录
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} 
                WHERE option_name LIKE %s",
                '_transient_timeout_' . $pattern
            )
        );
        

        
        self::update_cache_size();
        return intval($deleted);
    }
    
    /**
     * 清理所有Notion缓存 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @return int 清理的缓存数量
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::clear_all()
     */
    public static function clear_all(): int {
        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::clear_all();
        }

        // 后备实现（如果CacheManager不可用）
        global $wpdb;

        $deleted = $wpdb->query(
            "DELETE FROM {$wpdb->options}
            WHERE option_name LIKE '_transient_notion_cache_%'
            OR option_name LIKE '_transient_timeout_notion_cache_%'"
        );

        return intval($deleted);
    }
    
    /**
     * 检查缓存是否启用
     *
     * @since 2.0.0-beta.1
     * @return bool 缓存是否启用
     */
    public static function is_cache_enabled(): bool {
        $options = get_option('notion_to_wordpress_options', []);
        return $options['enable_smart_cache'] ?? true;
    }
    
    /**
     * 获取缓存统计信息 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @return array 缓存统计
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::get_cache_stats()
     */
    public static function get_cache_stats(): array {
        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::get_cache_stats();
        }

        // 后备实现（如果CacheManager不可用）
        return [
            'hits' => 0,
            'misses' => 0,
            'sets' => 0,
            'deletes' => 0,
            'size' => 0,
            'hit_rate' => 0
        ];
    }
    
    /**
     * 获取缓存大小
     *
     * @since 2.0.0-beta.1
     * @return int 缓存条目数量
     */
    private static function get_cache_size(): int {
        global $wpdb;
        
        $count = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_notion_cache_%'"
        );
        
        return intval($count);
    }
    
    /**
     * 更新缓存大小统计
     *
     * @since 2.0.0-beta.1
     */
    private static function update_cache_size(): void {
        self::$cache_stats['size'] = self::get_cache_size();
    }
    
    /**
     * 智能缓存策略判断 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @param string $endpoint API端点
     * @param array $params 请求参数
     * @return array 缓存策略信息
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::get_cache_strategy()
     */
    public static function get_cache_strategy(string $endpoint, array $params = []): array {
        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::get_cache_strategy($endpoint, $params);
        }

        // 后备实现（如果CacheManager不可用）
        // 用户信息 - 长期缓存
        if (strpos($endpoint, '/users/') !== false) {
            return [
                'type' => 'user_info',
                'cacheable' => true,
                'ttl' => self::CACHE_TYPES['user_info']['ttl']
            ];
        }

        // 数据库结构 - 中期缓存
        if (strpos($endpoint, '/databases/') !== false && !isset($params['filter'])) {
            return [
                'type' => 'database_structure',
                'cacheable' => true,
                'ttl' => self::CACHE_TYPES['database_structure']['ttl']
            ];
        }

        // 其他API响应 - 极短期缓存
        return [
            'type' => 'api_response',
            'cacheable' => true,
            'ttl' => self::CACHE_TYPES['api_response']['ttl']
        ];
    }

    // ========================================
    // 适配器说明
    // ========================================

    /**
     * SmartCache 现在作为 CacheManager 的适配器
     *
     * 所有缓存操作都委托给统一的 CacheManager 处理
     * 这确保了向后兼容性，同时利用了新的双层缓存架构
     *
     * 迁移建议：
     * - 新代码请直接使用 NTWP\Infrastructure\Cache\CacheManager
     * - 现有代码可以继续使用 SmartCache，但建议逐步迁移
     */

    /**
     * 二级缓存获取 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @param string $type 缓存类型
     * @param string $identifier 标识符
     * @param array $params 额外参数
     * @return mixed 缓存数据或false
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::get_tiered()
     */
    public static function get_tiered(string $type, string $identifier, array $params = []) {
        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::get_tiered($type, $identifier, $params);
        }

        // 后备实现（如果CacheManager不可用）
        return self::get($type, $identifier, $params);
    }

    /**
     * 二级缓存设置 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @param string $type 缓存类型
     * @param string $identifier 标识符
     * @param mixed $data 缓存数据
     * @param array $params 额外参数
     * @param int $custom_ttl 自定义TTL
     * @return bool 设置是否成功
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::set_tiered()
     */
    public static function set_tiered(string $type, string $identifier, $data, array $params = [], int $custom_ttl = null): bool {
        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::set_tiered($type, $identifier, $data, $params, $custom_ttl);
        }

        // 后备实现（如果CacheManager不可用）
        return self::set($type, $identifier, $data, $params, $custom_ttl);
    }

    /**
     * 设置L1缓存
     *
     * @since 2.0.0-beta.1
     * @param string $cache_key 缓存键
     * @param mixed $data 缓存数据
     */
    private static function set_l1_cache(string $cache_key, $data): void {
        // 检查L1缓存大小限制
        if (count(self::$l1_cache) >= self::L1_CACHE_SIZE_LIMIT) {
            // 移除最旧的缓存项
            $oldest_key = array_key_first(self::$l1_cache);
            unset(self::$l1_cache[$oldest_key]);
        }

        self::$l1_cache[$cache_key] = [
            'data' => $data,
            'timestamp' => time()
        ];
    }

    /**
     * 清理L1缓存
     *
     * @since 2.0.0-beta.1
     */
    public static function clear_l1_cache(): void {
        self::$l1_cache = [];
    }

    /**
     * 获取缓存层级统计 - 委托给CacheManager
     *
     * @since 2.0.0-beta.1
     * @return array 层级统计
     * @deprecated 2.0.0-beta.1 请使用 CacheManager::get_tiered_stats()
     */
    public static function get_tiered_stats(): array {
        // 委托给CacheManager
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return CacheManager::get_tiered_stats();
        }

        // 后备实现（如果CacheManager不可用）
        return [
            'l1_cache' => [
                'size' => 0,
                'limit' => self::L1_CACHE_SIZE_LIMIT,
                'usage_percent' => 0
            ],
            'l2_cache' => [
                'size' => 0,
                'type' => 'database'
            ],
            'total_cache_items' => 0,
            'cache_efficiency' => 0.0
        ];
    }

    // ========================================
    // 适配器模式实现完成
    // ========================================

    /**
     * SmartCache 适配器实现说明：
     *
     * 1. 所有公共方法都委托给 CacheManager
     * 2. 保持原有 API 接口不变，确保向后兼容
     * 3. 提供后备实现，防止 CacheManager 不可用
     * 4. 添加 @deprecated 标记，引导开发者迁移到新接口
     *
     * 性能优势：
     * - 利用 CacheManager 的 L1+L2 双层架构
     * - 智能缓存策略和 LRU 淘汰机制
     * - 统一的缓存管理和监控
     */
}
